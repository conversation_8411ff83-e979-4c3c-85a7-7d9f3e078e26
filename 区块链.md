## **区块链 (Blockchain)**
- 核心技术
  - 密码学（1970s起）
    - 哈希函数（1979年提出雏形） - 如SHA-256，用于链接区块，防篡改
    - 非对称加密（1976年诞生） - 用于数字签名和钱包地址
  - 分布式存储（1960s萌芽）
    - P2P网络（1999年Napster兴起） - 节点间直接通信和同步数据
  - 共识机制（2009年随区块链诞生）
    - 工作量证明 (PoW)
    - 权益证明 (PoS)
    - 其他机制 (DPoS, PBFT)
- 核心组件
  - 区块与链式结构 - 数据打包成块，通过哈希链接成链
  - 智能合约 - 自动执行的合约代码，程序化信任
- 主要特点
  - 去中心化 - 无中心机构，避免单点故障和操控
  - 不可篡改 - 一旦写入，数据极难修改
  - 透明与可追溯 - 账本公开，所有交易可查
- 应用领域
  - 数字资产
    - 稳定币 - 价值锚定法币的加密货币
    - RWA - 将房产、债券等现实资产上链
    - DeFi - 无需中介的开放式金融服务
  - 供应链管理 - 利用可追溯性，记录商品全流程
  - 身份认证 - 用户自主控制的去中心化身份 (DID)
- 区块链缺点
  - 性能效率低 - 交易处理速度慢
  - 能耗高 - PoW机制消耗大量能源
  - 监管难题 - 跨境监管存在漏洞
  - 技术门槛高 - 开发维护难度大
- 币圈KOL
  - 行业意见领袖 - 分享区块链知识、项目分析
  - 社群运营者 - 组织社区，推广项目
  - 投资风向标 - 影响用户投资决策